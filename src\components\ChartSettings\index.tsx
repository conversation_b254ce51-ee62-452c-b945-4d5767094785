'use client';

import type React from 'react';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { Collapse, Select, Checkbox, Input, Slider, ColorPicker, InputNumber } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import './index.less';
import { colorOptions } from '@/config/theme';
import { CHART_STYLE } from '@/config/charts';

const { Panel } = Collapse;
const { Option } = Select;

// 常量定义
const FONT_SIZE_OPTIONS = ['12', '14', '16', '18', '20'] as const;
const ANGLE_OPTIONS = ['0°', '30°', '45°', '60°', '90°'] as const;
const GRID_WIDTH_OPTIONS = ['1', '2', '3', '4', '5'] as const;
const GRID_STYLE_OPTIONS = [
  { value: '实线', label: '实线', className: 'solid' },
  { value: '虚线', label: '虚线', className: 'dashed' },
  { value: '点线', label: '点线', className: 'dotted' },
] as const;
const UNIT_INPUT_REGEX = /[^a-zA-Z\u4e00-\u9fa5%$¥€£℃°×÷±]/g;

// 工具函数
const mergeConfigValues = (defaultValues: Partial<ThemeConfigValues> = {}): Partial<ThemeConfigValues> => {
  return Object.entries(defaultValues).reduce((acc, [key, value]) => {
    if (value !== undefined && value !== null) {
      const typedKey = key as keyof ThemeConfigValues;
      (acc as any)[typedKey] = value;
    }
    return acc;
  }, {} as Partial<ThemeConfigValues>);
};

const handleNumberInput = (value: number | null, fallback: string = '自动'): string => {
  return value === null ? fallback : value.toString();
};

export interface ThemeConfigProps {
  onChange?: (config: ThemeConfigValues) => void;
  defaultValues?: Partial<ThemeConfigValues>;
  chartType?: 'bar' | 'horizontal-bar' | 'line' | 'pie' | 'scatter' | 'funnel' | 'combo' | 'word-cloud'; // 支持的图表类型：柱状图、条形图、折线图、饼图、散点图、漏斗图、组合图、词云图
  /** 布局模式：vertical-垂直布局，horizontal-左右两栏布局 */
  layout?: 'vertical' | 'horizontal';
}

export interface ThemeConfigValues {
  /** 图表主题 */
  theme: string;
  /** 图表宽度比例 */
  widthRatio: number;
  /** 是否显示图例 */
  showLegend: boolean;
  /** 是否显示数据标签 */
  showDataLabel: boolean;
  /** 数据标签文本大小 */
  dataLabelTextSize: string;
  /** 是否显示数据提示 */
  showDataTips: boolean;
  /** 是否显示前缀单位 */
  showPrefixUnit: boolean;
  /** 前缀单位文本 */
  prefixUnitText: string;
  /** 是否显示后缀单位 */
  showSuffixUnit: boolean;
  /** 后缀单位文本 */
  suffixUnitText: string;
  /** 是否显示坐标轴 */
  showAxis: boolean;
  /** 最大值 */
  maxValue: string;
  /** 最小值 */
  minValue: string;
  /** 数据间隔 */
  dataInterval: string;
  /** 坐标轴角度 */
  axisAngle: string;
  /** 文字大小 */
  textSize: string;
  /** x轴和y轴的颜色 */
  textColor: string;
  /** 是否显示网格 */
  showGrid: boolean;
  /** 网格样式 */
  gridStyle: string;
  /** 网格宽度 */
  gridWidth: string;
  /** 网格颜色 */
  gridColor: string;
  /** 是否显示动画 */
  showAnimation: boolean;
}

const ThemeConfig: React.FC<ThemeConfigProps> = ({ onChange, defaultValues = {}, chartType, layout = 'vertical' }) => {
  // 定义默认配置
  const defaultConfig: ThemeConfigValues = useMemo(() => CHART_STYLE, []);

  // 合并默认值和传入的配置
  const initialConfig = useMemo(
    () => ({
      ...defaultConfig,
      ...mergeConfigValues(defaultValues),
    }),
    [defaultConfig, defaultValues],
  );

  const [config, setConfig] = useState<ThemeConfigValues>(initialConfig);

  // 监听 defaultValues 变化
  useEffect(() => {
    const newConfig = {
      ...config,
      ...mergeConfigValues(defaultValues),
    };
    setConfig(newConfig);
  }, [defaultValues]);

  // 优化 handleChange 函数
  const handleChange = useCallback(
    <K extends keyof ThemeConfigValues>(key: K, value: ThemeConfigValues[K]) => {
      setConfig((prevConfig) => {
        const newConfig = { ...prevConfig, [key]: value };
        onChange?.(newConfig);
        return newConfig;
      });
    },
    [onChange],
  );

  // 优化 handleCheckboxChange 函数
  const handleCheckboxChange = useCallback(
    (e: CheckboxChangeEvent, key: keyof ThemeConfigValues) => {
      handleChange(key, e.target.checked as any);
    },
    [handleChange],
  );

  // 处理单位输入的函数
  const handleUnitInput = useCallback((value: string) => {
    return value.replace(UNIT_INPUT_REGEX, '');
  }, []);

  // 主题面板内容
  const renderThemePanel = () => (
    <Panel header="主题" key="theme" className="config-panel">
      <div className="config-item">
        <Select
          value={config.theme}
          onChange={(value) => handleChange('theme', value)}
          className="color-select"
          getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
        >
          {colorOptions.map((option) => (
            <Option key={option.value} value={option.value}>
              <div className="color-option">
                {option.colors.map((color, index) => (
                  <span key={index} className="color-block" style={{ backgroundColor: color }} />
                ))}
              </div>
            </Option>
          ))}
        </Select>
      </div>
      {chartType === 'bar' && (
        <div className="config-item">
          <span className="label">宽度比例</span>
          <div className="slider-container">
            <Slider value={config.widthRatio} onChange={(value) => handleChange('widthRatio', value)} className="width-slider" />
            <span className="slider-value">{config.widthRatio}%</span>
          </div>
        </div>
      )}
    </Panel>
  );

  // 左侧选项内容（图例、数据标签、数据提示）
  const renderLeftOptions = () => (
    <Panel header="选项" key="left-options" className="config-panel">
      <div className="options-container">
        <Checkbox checked={config.showLegend} onChange={(e) => handleCheckboxChange(e, 'showLegend')}>
          图例
        </Checkbox>
        <Checkbox checked={config.showDataLabel} onChange={(e) => handleCheckboxChange(e, 'showDataLabel')}>
          数据标签
        </Checkbox>
        {config.showDataLabel && (
          <div className="config-item">
            <span className="sub-label">文本</span>
            <Select value={config.dataLabelTextSize} onChange={(value) => handleChange('dataLabelTextSize', value)} className="text-input">
              {FONT_SIZE_OPTIONS.map((size) => (
                <Option key={size} value={size}>
                  {size}px
                </Option>
              ))}
            </Select>
          </div>
        )}
        <Checkbox checked={config.showDataTips} onChange={(e) => handleCheckboxChange(e, 'showDataTips')}>
          数据提示
        </Checkbox>
        <div className="config-item">
          <div className="unit-wrapper">
            <Checkbox checked={config.showPrefixUnit} onChange={(e) => handleCheckboxChange(e, 'showPrefixUnit')}>
              前缀单位
            </Checkbox>
            {config.showPrefixUnit && (
              <Input
                value={config.prefixUnitText}
                onChange={(e) => {
                  const value = handleUnitInput(e.target.value);
                  handleChange('prefixUnitText', value);
                }}
                placeholder="请输入前缀单位"
                className="unit-input"
                maxLength={10}
              />
            )}
          </div>
        </div>
        <div className="config-item">
          <div className="unit-wrapper">
            <Checkbox checked={config.showSuffixUnit} onChange={(e) => handleCheckboxChange(e, 'showSuffixUnit')}>
              后缀单位
            </Checkbox>
            {config.showSuffixUnit && (
              <Input
                value={config.suffixUnitText}
                onChange={(e) => {
                  const value = handleUnitInput(e.target.value);
                  handleChange('suffixUnitText', value);
                }}
                placeholder="请输入后缀单位"
                className="unit-input"
                maxLength={10}
              />
            )}
          </div>
        </div>
      </div>
    </Panel>
  );

  // 右侧选项内容（单位、坐标轴、网格、动画等）
  const renderRightOptions = () => (
    <Panel header="选项" key="right-options" className="config-panel">
      <div className="options-container">
        <Checkbox checked={config.showAxis} onChange={(e) => handleCheckboxChange(e, 'showAxis')}>
          坐标轴
        </Checkbox>
        {config.showAxis && (
          <div className="sub-options">
            <div className="config-item">
              <span className="sub-label">最大数值</span>
              <InputNumber
                value={config.maxValue === '自动' ? undefined : Number(config.maxValue)}
                onChange={(value) => handleChange('maxValue', handleNumberInput(value))}
                placeholder="自动"
                className="axis-input"
                min={0}
              />
            </div>
            <div className="config-item">
              <span className="sub-label">最小数值</span>
              <InputNumber
                value={config.minValue === '自动' ? undefined : Number(config.minValue)}
                onChange={(value) => handleChange('minValue', handleNumberInput(value))}
                placeholder="自动"
                className="axis-input"
                min={0}
              />
            </div>
            <div className="config-item">
              <span className="sub-label">数据间距</span>
              <InputNumber
                value={config.dataInterval === '自动' ? undefined : Number(config.dataInterval)}
                onChange={(value) => handleChange('dataInterval', handleNumberInput(value))}
                placeholder="自动"
                className="axis-input"
                min={0}
              />
            </div>
            <div className="config-item">
              <span className="sub-label">轴标签角度</span>
              <Select value={config.axisAngle} onChange={(value) => handleChange('axisAngle', value)} className="axis-input">
                <Option value="0°">0°</Option>
                <Option value="30°">30°</Option>
                <Option value="45°">45°</Option>
                <Option value="60°">60°</Option>
                <Option value="90°">90°</Option>
              </Select>
            </div>
            <div className="config-item">
              <span className="sub-label">文本</span>
              <Select value={config.textSize} onChange={(value) => handleChange('textSize', value)} className="text-input">
                <Option value="12">12px</Option>
                <Option value="14">14px</Option>
                <Option value="16">16px</Option>
                <Option value="18">18px</Option>
                <Option value="20">20px</Option>
              </Select>
              <ColorPicker value={config.textColor} onChange={(color) => handleChange('textColor', color.toHexString())} trigger="hover">
                <div className="color-block text-color" style={{ backgroundColor: config.textColor }}></div>
              </ColorPicker>
            </div>
          </div>
        )}
        <Checkbox checked={config.showGrid} onChange={(e) => handleCheckboxChange(e, 'showGrid')}>
          网格
        </Checkbox>
        {config.showGrid && (
          <div className="sub-options">
            <div className="config-item">
              <Select
                value={config.gridStyle}
                onChange={(value) => handleChange('gridStyle', value)}
                className="grid-style-select"
                optionLabelProp="label"
              >
                <Option
                  value="实线"
                  label={
                    <div className="line-style-preview">
                      <div className="line solid" />
                      <span className="text">实线</span>
                    </div>
                  }
                >
                  <div className="line-style-preview">
                    <div className="line solid" />
                    <span className="text">实线</span>
                  </div>
                </Option>
                <Option
                  value="虚线"
                  label={
                    <div className="line-style-preview">
                      <div className="line dashed" />
                      <span className="text">虚线</span>
                    </div>
                  }
                >
                  <div className="line-style-preview">
                    <div className="line dashed" />
                    <span className="text">虚线</span>
                  </div>
                </Option>
                <Option
                  value="点线"
                  label={
                    <div className="line-style-preview">
                      <div className="line dotted" />
                      <span className="text">点线</span>
                    </div>
                  }
                >
                  <div className="line-style-preview">
                    <div className="line dotted" />
                    <span className="text">点线</span>
                  </div>
                </Option>
              </Select>
              <Select
                value={config.gridWidth}
                onChange={(value) => handleChange('gridWidth', value)}
                className="grid-width-select"
                style={{ width: '100%' }}
              >
                <Option value="1">1px</Option>
                <Option value="2">2px</Option>
                <Option value="3">3px</Option>
                <Option value="4">4px</Option>
                <Option value="5">5px</Option>
              </Select>
              <ColorPicker value={config.gridColor} onChange={(color) => handleChange('gridColor', color.toHexString())} trigger="hover">
                <div className="color-block grid-color" style={{ backgroundColor: config.gridColor }}></div>
              </ColorPicker>
            </div>
          </div>
        )}
        <Checkbox checked={config.showAnimation} onChange={(e) => handleCheckboxChange(e, 'showAnimation')}>
          动画
        </Checkbox>
      </div>
    </Panel>
  );

  return (
    <div className={`theme-config ${layout}-layout`}>
      {layout === 'horizontal' ? (
        <div className="horizontal-panels">
          <div className="left-panel">
            <Collapse
              bordered={false}
              defaultActiveKey={['theme', 'left-options']}
              expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
            >
              {renderThemePanel()}
              {renderLeftOptions()}
            </Collapse>
          </div>
          <div className="right-panel">
            <Collapse
              bordered={false}
              defaultActiveKey={['right-options']}
              expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
            >
              {renderRightOptions()}
            </Collapse>
          </div>
        </div>
      ) : (
        <Collapse
          bordered={false}
          defaultActiveKey={['theme', 'left-options', 'right-options']}
          expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        >
          {renderThemePanel()}
          {renderLeftOptions()}
          {renderRightOptions()}
        </Collapse>
      )}
    </div>
  );
};

export default ThemeConfig;
